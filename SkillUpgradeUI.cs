using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;
using System.Collections;
using HeroSystem;

public class SkillUpgradeUI : MonoBehaviour
{
    [SerializeField] private Image[] skillImages;
    [SerializeField] private TextMeshProUGUI[] skillLevelTexts;
    [SerializeField] private GameObject[] skillGlowObjects;
    [SerializeField] private Button[] skillButtons;
    [SerializeField] private Button closeButton;
    [SerializeField] private GameObject skillDetailsPanel;
    [SerializeField] private TextMeshProUGUI selectedSkillName;
    [SerializeField] private TextMeshP<PERSON><PERSON>G<PERSON> primaryDescription;
    [SerializeField] private TextMeshP<PERSON><PERSON>G<PERSON> secondaryDescription;
    [SerializeField] private HeroSelector heroSelector;
    [SerializeField] private GameObject skillUpgradePanel;
    [SerializeField] private CameraZoom cameraZoom;
    [SerializeField] private Image selectedSkillImage;
    [SerializeField] private But<PERSON> primaryDescButton;
    [SerializeField] private Button secondaryDescButton;
    [SerializeField] private GameObject primaryDescPanel;
    [SerializeField] private GameObject secondaryDescPanel;
    [SerializeField] private Image fragmentImage;
    [SerializeField] private TextMeshProUGUI fragmentText;
    [SerializeField] private Slider fragmentProgressSlider;
    [SerializeField] private Button increaseFragmentButton;
    [SerializeField] private Button levelUpButton;
    [SerializeField] private GameObject insufficientFragmentsMessage;
    [SerializeField] private TextMeshProUGUI skillNameText;
    [SerializeField] private TextMeshProUGUI skillDescriptionText;
    [SerializeField] private TextMeshProUGUI skillSecondaryDescriptionText;
    [SerializeField] private Image skillIconImage;

    [Header("Fragment Conversion")]
    [SerializeField] private Image generalFragmentImage;
    [SerializeField] private TextMeshProUGUI generalFragmentQuantityText;
    [SerializeField] private TMP_InputField conversionAmountInput;
    [SerializeField] private Button increaseButton;
    [SerializeField] private Button decreaseButton;
    [SerializeField] private Button exchangeButton;
    [SerializeField] private Transform objectOne;
    [SerializeField] private Transform objectTwo;

    private HeroData selectedHero;
    private int selectedSkillIndex = -1;
    private Vector3 normalSkillScale = Vector3.one;
    private Vector3 selectedSkillScale = new Vector3(1.2f, 1.2f, 1.2f);
    private int fragmentsToUse = 0;
    private InventoryItem heroFragment;
    private InventoryItem generalFragment;
    private int conversionAmount = 0;
    private Vector3 objectOneDefaultScale;
    private Vector3 objectTwoDefaultScale;
    private Vector3 objectOneDefaultPosition;
    private Vector3 objectTwoDefaultPosition;


    private void Awake()
    {
        // Initialize arrays if needed
        if (skillImages == null || skillImages.Length == 0)
        {
            skillImages = GetComponentsInChildren<Image>();
        }
    }
    private void Start()
    {
        closeButton.onClick.AddListener(CloseSkillUpgradeUI);
        primaryDescButton.onClick.AddListener(() => SwitchDescription(true));
        secondaryDescButton.onClick.AddListener(() => SwitchDescription(false));
        levelUpButton.onClick.AddListener(OnLevelUpButtonClicked);
        increaseFragmentButton.onClick.AddListener(OnIncreaseFragmentButtonClicked);
        increaseButton.onClick.AddListener(IncreaseConversionAmount);
        decreaseButton.onClick.AddListener(DecreaseConversionAmount);
        exchangeButton.onClick.AddListener(ExchangeFragments);
        conversionAmountInput.onValueChanged.AddListener(OnConversionAmountChanged);
        objectOneDefaultScale = objectOne.localScale;
        objectTwoDefaultScale = objectTwo.localScale;
        objectOneDefaultPosition = objectOne.localPosition;
        objectTwoDefaultPosition = objectTwo.localPosition;

         // Add click handlers for each skill button
        for (int i = 0; i < skillButtons.Length; i++)
        {
            int index = i; // Capture the index for the lambda
            skillButtons[i].onClick.AddListener(() => OnSkillSelected(index));
        }
        UpdateSkillUI();
    }

    private void Update()
    {
        // Update skill glow objects
        for (int i = 0; i < skillGlowObjects.Length; i++)
        {
            if (i == selectedSkillIndex)
            {
                skillGlowObjects[i].gameObject.SetActive(true);
            }
            else
            {
                skillGlowObjects[i].gameObject.SetActive(false);
            }
        }
    }
    private void OnSkillSelected(int index)
    {
        if (index < 0 || index >= skillImages.Length || selectedHero == null) return;

        fragmentsToUse = 0;

        // Reset previous selection visual
        if (selectedSkillIndex >= 0 && selectedSkillIndex < skillImages.Length)
        {
            skillImages[selectedSkillIndex].transform.localScale = normalSkillScale;
            skillGlowObjects[selectedSkillIndex].SetActive(false);
        }

        selectedSkillIndex = index;

        // Apply selection visual
        skillImages[index].transform.localScale = selectedSkillScale;
        skillGlowObjects[index].SetActive(true);
        skillDetailsPanel.SetActive(true);

        // Update skill details
        SkillData selectedSkill = selectedHero.Skills[index];
        selectedSkillImage.sprite = selectedSkill.skillIcon;
        selectedSkillName.text = selectedSkill.skillName;
        primaryDescription.text = selectedSkill.description;
        secondaryDescription.text = selectedSkill.secondaryDescription;

        // Show primary description by default
        SwitchDescription(true);

        // Update level up button state
        levelUpButton.interactable = IsSkillUpgradeable(index);
        UpdateFragmentUI();
    }

    private void SwitchDescription(bool isPrimary)
    {
        primaryDescPanel.SetActive(isPrimary);
        secondaryDescPanel.SetActive(!isPrimary);

        // Visual feedback for selected tab
        primaryDescButton.interactable = !isPrimary;
        secondaryDescButton.interactable = isPrimary;
    }

    public void OpenSkillUpgradeUI(HeroData hero)
    {
        selectedHero = hero;
        selectedSkillIndex = -1; // Reset selection

        // Initialize skill icons and levels
        for (int i = 0; i < skillImages.Length && i < hero.Skills.Length; i++)
        {
            // Enable the parent object
            skillButtons[i].transform.parent.gameObject.SetActive(true);

            // Set skill icons
            skillImages[i].sprite = hero.Skills[i].skillIcon;
            skillImages[i].gameObject.SetActive(true);

            // Set initial level text
            int level = hero.GetSkillLevel(i);
            if (i == 0 && level < 1) level = 1; // First skill always at least level 1

            bool isSkillActive;
            if (i == 0)
            {
                isSkillActive = true;
            }
            else if (i < 4)
            {
                isSkillActive = hero.Rank > i;
            }
            else
            {
                isSkillActive = hero.Skills.Take(4).All(s => hero.GetSkillLevel(i) == 5);
            }

            skillLevelTexts[i].text = isSkillActive ? $"{level}/5" : "Locked";

            // Reset glow effects
            skillGlowObjects[i].SetActive(false);

            // Enable the button
            skillButtons[i].gameObject.SetActive(true);
        }

        // Deactivate any remaining skill slots
        for (int i = hero.Skills.Length; i < skillImages.Length; i++)
        {
            skillButtons[i].transform.parent.gameObject.SetActive(false);
        }

        // Select first skill by default
        OnSkillSelected(0);

        UpdateConversionUI();
        skillUpgradePanel.SetActive(true);
        StartCoroutine(SmoothTransform(objectOne, objectOneDefaultScale, new Vector3(1.2f, 1.2f, 1.2f),
            objectOneDefaultPosition, new Vector3(100f, 0f, 0f), 0.4f));
        StartCoroutine(SmoothTransform(objectTwo, objectTwoDefaultScale, new Vector3(1.5f, 1.5f, 1.5f),
            objectTwoDefaultPosition, new Vector3(-250f, -250f, 0f), 0.4f));
        heroSelector.HideUIElements();
    }

    private void UpdateSkillUI()
    {
        if (selectedHero == null) return;

        // Update all skill icons and levels
        for (int i = 0; i < skillImages.Length && i < selectedHero.Skills.Length; i++)
        {
            // Update skill icons
            skillImages[i].sprite = selectedHero.Skills[i].skillIcon;

            // Determine if skill is active/unlocked
            bool isSkillActive;
            if (i == 0)
            {
                isSkillActive = true; // First skill always active
            }
            else if (i < 4)
            {
                isSkillActive = selectedHero.Rank > i; // Skills 2-4 require higher ranks
            }
            else // Fifth skill
            {
                isSkillActive = selectedHero.Skills.Take(4).All(s => selectedHero.GetSkillLevel(i) == 5);
            }

            // Get actual skill level from GameManager
            int level = GameManager.Instance.GetSkillLevel(selectedHero.HeroName, selectedHero.Skills[i].skillName);

            // Update level display
            if (isSkillActive)
            {
                if (i == 0 && level < 1) level = 1; // First skill always at least level 1
                skillLevelTexts[i].text = $"{level}/5";
            }
            else
            {
                skillLevelTexts[i].text = " ";
            }

            // Update visual state
            skillImages[i].material.SetFloat("_GrayscaleAmount", isSkillActive ? 0f : 1f);
            skillGlowObjects[i].SetActive(i == selectedSkillIndex);

        }

        // Update detail panel if skill is selected
        if (selectedSkillIndex >= 0 && selectedSkillIndex < selectedHero.Skills.Length)
        {
            SkillData currentSkill = selectedHero.Skills[selectedSkillIndex];
            selectedSkillImage.sprite = currentSkill.skillIcon;
            selectedSkillName.text = currentSkill.skillName;
            primaryDescription.text = currentSkill.description;
            secondaryDescription.text = currentSkill.secondaryDescription;

            levelUpButton.interactable = IsSkillUpgradeable(selectedSkillIndex);
            UpdateFragmentUI();
        }
    }



    private bool IsSkillUpgradeable(int skillIndex)
    {
        if (skillIndex < 0 || skillIndex >= selectedHero.Skills.Length)
            return false;

        // First skill is always upgradeable if not max level
        if (skillIndex == 0)
            return selectedHero.GetSkillLevel(skillIndex) < 5;

        // Skills 2-4 require higher ranks
        if (skillIndex < 4)
            return selectedHero.Rank > skillIndex && selectedHero.GetSkillLevel(skillIndex) < 5;

        // 5th skill requires all others at max level
        return selectedHero.Skills.Take(4).All(s => selectedHero.GetSkillLevel(skillIndex) == 5)
            && selectedHero.GetSkillLevel(skillIndex) < 5;
    }


    private void CloseSkillUpgradeUI()
    {
        skillUpgradePanel.GetComponent<UIElementAnimator>().HandleDeactivation();
        StartCoroutine(SmoothTransform(objectOne, objectOne.localScale, objectOneDefaultScale,
            objectOne.localPosition, objectOneDefaultPosition, 0.4f));
        StartCoroutine(SmoothTransform(objectTwo, objectTwo.localScale, objectTwoDefaultScale,
            objectTwo.localPosition, objectTwoDefaultPosition, 0.4f));
        heroSelector.ShowUIElements();
    }

    private int GetRequiredFragments(int skillIndex, int currentLevel)
    {
        // First skill starts at level 1, so we need to adjust the array index accordingly
        int arrayIndex = currentLevel - 1;

        // Return 0 if max level or invalid index
        if (arrayIndex < 0 || currentLevel >= 5) return 0;

        // For first skill, if it's at initial level 1, use first upgrade cost
        if (skillIndex == 0 && currentLevel == 1)
        {
            arrayIndex = 0;  // Use first upgrade cost (level 1 -> 2)
        }

        switch (selectedHero.Rarity)
        {
            case HeroRarity.Legendary:
                int[][] legendaryRequirements = new int[][]
                {
                    new int[] { 15, 20, 25, 30 },      // Skill 1
                    new int[] { 15, 20, 25, 30 },      // Skill 2
                    new int[] { 20, 25, 30, 35 },      // Skill 3
                    new int[] { 25, 30, 35, 40 },      // Skill 4
                    new int[] { 80, 90, 100, 110 }     // Skill 5
                };
                return legendaryRequirements[skillIndex][arrayIndex];

            case HeroRarity.Elite:
                int[][] eliteRequirements = new int[][]
                {
                    new int[] { 10, 15, 20, 25 },      // Skill 1
                    new int[] { 10, 15, 20, 25 },      // Skill 2
                    new int[] { 15, 20, 25, 30 },      // Skill 3
                    new int[] { 20, 25, 30, 35 },      // Skill 4
                    new int[] { 40, 50, 60, 70 }       // Skill 5
                };
                return eliteRequirements[skillIndex][arrayIndex];

            case HeroRarity.Rare:
                int[][] rareRequirements = new int[][]
                {
                    new int[] { 5, 10, 15, 20 },       // Skill 1
                    new int[] { 5, 10, 15, 20 },       // Skill 2
                    new int[] { 10, 15, 20, 25 },      // Skill 3
                    new int[] { 15, 20, 25, 30 },      // Skill 4
                    new int[] { 30, 40, 50, 60 }       // Skill 5
                };
                return rareRequirements[skillIndex][arrayIndex];

            default:
                return 0;
        }
    }

    private void UpdateFragmentUI()
    {
        int currentLevel = selectedHero.GetSkillLevel(selectedSkillIndex);
        int requiredFragments = GetRequiredFragments(selectedSkillIndex, currentLevel);

        // Debug log fragment requirements
        Debug.Log($"UpdateFragmentUI - Current Level: {currentLevel}, Required Fragments: {requiredFragments}");

        heroFragment = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Other)
            .FirstOrDefault(item => item.itemSO.Tag == selectedHero.HeroName);

        int availableFragments = heroFragment?.quantity ?? 0;

        // Update UI elements
        fragmentImage.sprite = heroFragment?.itemSO.Icon;
        fragmentText.text = $"{availableFragments}/{requiredFragments}";
        fragmentProgressSlider.value = (float)fragmentsToUse / requiredFragments;

        // Update buttons
        increaseFragmentButton.interactable = fragmentsToUse < availableFragments;
        levelUpButton.interactable = availableFragments >= requiredFragments && IsSkillUpgradeable(selectedSkillIndex);
    }

    public void OnIncreaseFragmentButtonClicked()
    {
        fragmentsToUse++;
        UpdateFragmentUI();
    }

    public void OnLevelUpButtonClicked()
    {
        int currentLevel = selectedHero.GetSkillLevel(selectedSkillIndex);
        int requiredFragments = GetRequiredFragments(selectedSkillIndex, currentLevel);

        if (heroFragment != null && heroFragment.quantity >= requiredFragments)
        {

            // Deduct fragments from inventory
            heroFragment.quantity -= requiredFragments;

            // Upgrade the skill level
            if (selectedHero.UpgradeSkill(selectedSkillIndex))
            {
                // Debug log after upgrade
                int newLevel = selectedHero.GetSkillLevel(selectedSkillIndex);
                Debug.Log($"After upgrade - Skill: {selectedHero.Skills[selectedSkillIndex].skillName}, New Level: {newLevel}");

                // Update UI
                UpdateSkillUI();
                UpdateFragmentUI();

                // Notify other UIs through GameManager
                GameManager.Instance.UpdateHeroProgress(selectedHero);

                // Force refresh other UIs
                var heroSelector = FindFirstObjectByType<HeroSelector>();
                if (heroSelector != null && heroSelector.gameObject.activeInHierarchy)
                {
                    heroSelector.UpdateSelectedHeroUI();
                }

                var expeditionFPS = FindFirstObjectByType<ExpeditionFPS>();
                if (expeditionFPS != null && expeditionFPS.gameObject.activeInHierarchy)
                {
                    expeditionFPS.DisplayCurrentHero();
                }
            }
            else
            {
                Debug.LogError("Failed to upgrade skill!");
            }
        }
        else
        {
            insufficientFragmentsMessage.SetActive(true);
            StartCoroutine(HideInsufficientMessage());
        }
    }

    private IEnumerator HideInsufficientMessage()
    {
        yield return new WaitForSeconds(2f);
        insufficientFragmentsMessage.SetActive(false);
    }
    private void UpdateConversionUI()
    {
        string fragmentTag = $"{selectedHero.Rarity} Fragment";
        generalFragment = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Other)
            .FirstOrDefault(item => item.itemSO.Tag == fragmentTag);

        if (generalFragment != null)
        {
            generalFragmentImage.sprite = generalFragment.itemSO.Icon;
            generalFragmentQuantityText.text = generalFragment.quantity.ToString();

            conversionAmountInput.text = conversionAmount.ToString();
            increaseButton.interactable = conversionAmount < generalFragment.quantity;
            decreaseButton.interactable = conversionAmount > 0;
            exchangeButton.interactable = conversionAmount > 0 && conversionAmount <= generalFragment.quantity;
        }
    }

    public void OnConversionAmountChanged(string value)
    {
        if (int.TryParse(value, out int amount))
        {
            conversionAmount = Mathf.Clamp(amount, 0, generalFragment?.quantity ?? 0);
            conversionAmountInput.text = conversionAmount.ToString();
            UpdateConversionUI();
        }
    }

    public void IncreaseConversionAmount()
    {
        if (generalFragment != null && conversionAmount < generalFragment.quantity)
        {
            conversionAmount++;
            UpdateConversionUI();
        }
    }

    public void DecreaseConversionAmount()
    {
        if (conversionAmount > 0)
        {
            conversionAmount--;
            UpdateConversionUI();
        }
    }

    public void ExchangeFragments()
    {
        if (generalFragment != null && conversionAmount > 0 && conversionAmount <= generalFragment.quantity)
        {
            generalFragment.quantity -= conversionAmount;
            heroFragment.quantity += conversionAmount;
            conversionAmount = 0;
            UpdateConversionUI();
            UpdateFragmentUI();
        }
    }

    private IEnumerator SmoothTransform(Transform target, Vector3 startScale, Vector3 endScale,
    Vector3 startPos, Vector3 endPos, float duration)
    {
        float elapsed = 0;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            target.localScale = Vector3.Lerp(startScale, endScale, progress);
            target.localPosition = Vector3.Lerp(startPos, endPos, progress);

            yield return null;
        }

        target.localScale = endScale;
        target.localPosition = endPos;
    }

}

