using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;
using System.Collections.Generic;
using System.Collections;
using HeroSystem;

public class HeroSelector : MonoBehaviour
{
    [SerializeField] private Transform content; // Parent container with vertical layout
    [SerializeField] private Transform unlockedHeroesContainer; // Container for unlocked heroes
    [SerializeField] private Transform lockedHeroesContainer; // Container for locked heroes
    [SerializeField] private HeroThumbnailItem thumbnailPrefab; // Prefab for hero thumbnails
    [SerializeField] private Image selectedHeroThumbnail; // Image to display the selected hero's thumbnail
    [SerializeField] private TextMeshProUGUI selectedHeroName; // Text to display the selected hero's name
    [SerializeField] private TextMesh<PERSON>roUGUI selectedNickName; // Text to display the selected hero's name
    [SerializeField] private TextMeshProUGUI selectedHeroType; // Text to display the selected hero's type
    [SerializeField] private TextMesh<PERSON><PERSON><PERSON>G<PERSON> selectedHeroGeneration; // Text to display the selected hero's generation
    [Serialize<PERSON>ield] private TextMeshProUG<PERSON> selectedHeroPower; // Text to display the selected hero's current power
    [SerializeField] private Text<PERSON>esh<PERSON><PERSON><PERSON><PERSON><PERSON> selectedHeroStats; // Text to display the selected hero's base stats
    [SerializeField] private TextMeshProUGUI selectedHeroRank; // Text to display the selected hero's rank
    [SerializeField] private TextMeshProUGUI selectedHeroExpLevel; // Text to display the selected hero's exp level
    [SerializeField] private TextMeshProUGUI usedBooksText; // Text to display the used books and required books for the next level
    // [SerializeField] private Transform heroPrefabContainer; // Container to instantiate the hero prefab
    [SerializeField] private Button upgradeRankButton; // Button to open the hero rank UI
    [SerializeField] private Button upgradeExpButton; // Button to open the hero exp UI
    [SerializeField] private Button upgradeSkillsButton; // Button to open the skill upgrade UI
    [SerializeField] private Image[] skillImages; // Images to display the hero's skills
    [SerializeField] private SkillUpgradeUI skillUpgradeUI; // Reference to the Skill Upgrade UI
    [SerializeField] private Image[] rankStars; // Images to display the hero's rank as stars
    [SerializeField] private GameObject heroUI; // Reference to the Hero UI
    [SerializeField] private GameObject heroLevelUpUI; // Reference to the Hero Level Up UI
    [SerializeField] private GameObject heroRankUI; // Reference to the Hero Level Up UI
    [SerializeField] private Transform bookListContainer; // Container for the book list
    [SerializeField] private Transform badgeListContainer; // Container for the badge list
    [SerializeField] private Button closeHeroButton; // Button to close the Hero UI
    [SerializeField] private Button closeHeroLevelUpButton; // Button to close the Hero Level Up UI
    [SerializeField] private Button closeHeroRankButton; // Button to close the Hero Rank UI
    [SerializeField] private Button levelUpButton; // Button to level up the hero
    [SerializeField] private Image expProgressBar; // Reference to the circular progress bar
    [SerializeField] private Image ptsProgressBar; // Reference to the PTs progress bar
    [SerializeField] private TextMeshProUGUI ptsProgressText; // Text to display the PTs progress
    [SerializeField] private Transform statsGrid; // Reference to the GridLayoutGroup
    [SerializeField] private GameObject statRowPrefab; // Prefab for the stat row containing label and value
    [SerializeField] private RawImage heroDisplayRawImage; // RawImage to display the hero's Render Texture
    [SerializeField] private VideoSequencePlayer videoSequencePlayer;
    [SerializeField] private Image fallbackImageDisplay; // Add this UI Image component
    // [SerializeField] private Camera heroDisplayCamera; // Camera to render the hero to the Render Texture
    [SerializeField] private GameObject separator; // UI element to separate unlocked and locked heroes
    [SerializeField] private TextMeshProUGUI fragmentText; // Text to display available/required fragments
    [SerializeField] private Button recruitButton; // Button to recruit the hero
    [SerializeField] private Image fragmentImage; // Image to display the fragment used to unlock the hero
    [SerializeField] private TextMeshProUGUI unlockedHeroesCountText; // Text to display the number of unlocked heroes
    [SerializeField] private TMP_Dropdown sortingDropdown; // Dropdown to select sorting method
    [SerializeField] private GameObject heroPanel; // Reference to the Hero Panel
    [SerializeField] private GameObject heroSort; // Reference to the Hero Sort
    [SerializeField] private GameObject scrollView; // Reference to the Scroll View
    [SerializeField] private GameObject heroPower; // Reference to the Scroll View
    [SerializeField] private Button rankUpButton; // Button to rank up the hero
    [SerializeField] private CameraZoom cameraZoom; // Reference to the CameraZoom script
    [SerializeField] private CameraControl cameraControl; // Add reference to camera control
    [SerializeField] private Transform objectOne;
    [SerializeField] private Transform objectTwo;
    private UIElementAnimator uiElementAnimator;

    private Vector3 objectOneDefaultScale;
    private Vector3 objectTwoDefaultScale;
    private Vector3 objectOneDefaultPosition;
    private Vector3 objectTwoDefaultPosition;
    private HeroData selectedHero; // Currently selected hero
    private HeroThumbnailItem selectedThumbnailItem; // Currently selected thumbnail item
    private Dictionary<HeroData, int> heroExpGained;
    private Dictionary<HeroData, int> heroPtsGained;
    private HeroData[] allHeroes;
    private string currentSortingMethod = "Power"; // Default sorting method

    private void Awake()
    {
        heroExpGained = new Dictionary<HeroData, int>();
        heroPtsGained = new Dictionary<HeroData, int>();
    }

    private void Start()
    {
        if (InventorySystem.Instance == null)
        {
            Debug.LogError("InventorySystem instance not found!");
            return;
        }
        PopulateHeroList();
        upgradeRankButton.onClick.AddListener(OnUpgradeRankButtonClicked);
        upgradeRankButton.onClick.AddListener(HideUIElements);
        upgradeExpButton.onClick.AddListener(OnUpgradeExpButtonClicked);
        upgradeSkillsButton.onClick.AddListener(OnUpgradeSkillsButtonClicked);
        closeHeroButton.onClick.AddListener(CloseHeroUI);
        closeHeroLevelUpButton.onClick.AddListener(CloseHeroLevelUpUI);
        levelUpButton.onClick.AddListener(OnLevelUpButtonClicked); // Add listener for the LevelUp button
        recruitButton.onClick.AddListener(OnRecruitButtonClicked); // Add listener for the Recruit button
        sortingDropdown.onValueChanged.AddListener(OnSortingMethodChanged); // Add listener for the sorting dropdown
        closeHeroRankButton.onClick.AddListener(CloseHeroRankUI);
        closeHeroRankButton.onClick.AddListener(ShowUIElements);
        rankUpButton.onClick.AddListener(OnRankUpButtonClicked); // Add listener for the RankUp button
        objectOneDefaultScale = objectOne.localScale;
        objectTwoDefaultScale = objectTwo.localScale;
        objectOneDefaultPosition = objectOne.localPosition;
        objectTwoDefaultPosition = objectTwo.localPosition;
        UpdateUnlockedHeroesCount();

        uiElementAnimator = heroUI.GetComponent<UIElementAnimator>();
        CameraControl.Instance.RegisterUI(heroUI);

    }

    private void PopulateHeroList()
    {
        // Clear existing thumbnails
        foreach (Transform child in unlockedHeroesContainer)
        {
            Destroy(child.gameObject);
        }
        foreach (Transform child in lockedHeroesContainer)
        {
            Destroy(child.gameObject);
        }

        allHeroes = Resources.LoadAll<HeroData>("Heroes");
        Debug.Log("Number of heroes loaded: " + allHeroes.Length);

        // Sort heroes by lock status and the selected sorting method
        SortHeroes();

        // Add unlocked heroes
        foreach (HeroData hero in allHeroes.Where(hero => !hero.IsLocked))
        {
            Debug.Log("Loading hero: " + hero.HeroName);
            HeroThumbnailItem item = Instantiate(thumbnailPrefab, unlockedHeroesContainer);
            item.Initialize(hero, this);

            // Select the first unlocked hero by default
            if (selectedHero == null)
            {
                OnHeroSelected(hero, item);
            }
        }

        // Enable the separator if there are both unlocked and locked heroes
        separator.SetActive(allHeroes.Any(hero => !hero.IsLocked) && allHeroes.Any(hero => hero.IsLocked));

        // Add locked heroes
        foreach (HeroData hero in allHeroes.Where(hero => hero.IsLocked))
        {
            Debug.Log("Loading hero: " + hero.HeroName);
            HeroThumbnailItem item = Instantiate(thumbnailPrefab, lockedHeroesContainer);
            item.Initialize(hero, this);
        }

        // Force layout rebuild to ensure containers resize correctly
        LayoutRebuilder.ForceRebuildLayoutImmediate(content.GetComponent<RectTransform>());
    }

    private void SortHeroes()
    {
        switch (currentSortingMethod)
        {
            case "Power":
                allHeroes = allHeroes.OrderBy(hero => hero.IsLocked).ThenByDescending(hero => hero.CurrentPower).ToArray();
                break;
            case "Rank":
                allHeroes = allHeroes.OrderBy(hero => hero.IsLocked).ThenByDescending(hero => hero.Rank).ToArray();
                break;
            case "Level":
                allHeroes = allHeroes.OrderBy(hero => hero.IsLocked).ThenByDescending(hero => hero.ExpLevel).ToArray();
                break;
        }
    }

    private void OnSortingMethodChanged(int index)
    {
        currentSortingMethod = sortingDropdown.options[index].text;
        sortingDropdown.captionText.text = currentSortingMethod; // Update the dropdown placeholder
        PopulateHeroList(); // Re-populate the hero list with the new sorting method
    }

    private void UpdateUnlockedHeroesCount()
    {
        int unlockedHeroesCount = allHeroes.Count(hero => !hero.IsLocked);
        unlockedHeroesCountText.text = $"{unlockedHeroesCount}";
    }

    public HeroData[] GetAllHeroes()
    {
        return allHeroes;
    }

    public void OnHeroSelected(HeroData selectedHero, HeroThumbnailItem thumbnailItem)
    {
        this.selectedHero = selectedHero;

        // Initialize dictionary entries if they don't exist
        if (!heroExpGained.ContainsKey(selectedHero))
        {
            heroExpGained[selectedHero] = selectedHero.CurrentExp;
        }
        if (!heroPtsGained.ContainsKey(selectedHero))
        {
            heroPtsGained[selectedHero] = 0;
        }

        // Load hero progress from GameManager
        GameManager.Instance.ApplyHeroProgress(selectedHero);

        // Pass true to setup the video when a hero is initially selected
        // Also reload progress since we just applied it from GameManager
        UpdateSelectedHeroUI(true, true);
        // InstantiateHeroPrefab(); // We can remove this if needed for other purposes
        PopulateBadges();

        // Update the frame for the selected thumbnail
        if (selectedThumbnailItem != null)
        {
            selectedThumbnailItem.SetSelected(false);
        }
        selectedThumbnailItem = thumbnailItem;
        selectedThumbnailItem.SetSelected(true);
    }

    public void UpdateSelectedHeroUI(bool setupVideo = false, bool reloadProgress = true)
    {
        if (selectedHero == null) return;

        // Only reload progress from GameManager when explicitly requested
        // This prevents overwriting changes made during level up or rank up
        if (reloadProgress)
        {
            GameManager.Instance.ApplyHeroProgress(selectedHero);
        }

        selectedHeroThumbnail.sprite = selectedHero.HeroThumbnail;
        selectedHeroName.text = selectedHero.HeroName;
        selectedNickName.text = selectedHero.NickName;
        selectedHeroType.text = selectedHero.CurrentHeroType.ToString();
        selectedHeroGeneration.text = "Gen " + selectedHero.HeroGeneration.ToString();
        selectedHeroPower.text = selectedHero.CurrentPower.ToString();
        selectedHeroRank.text = selectedHero.Rank.ToString();
        selectedHeroExpLevel.text = selectedHero.ExpLevel.ToString();

        // Clear existing stats
        foreach (Transform child in statsGrid)
        {
            Destroy(child.gameObject);
        }

        // Initialize exp/pts if needed
        if (!heroExpGained.ContainsKey(selectedHero))
        {
            heroExpGained[selectedHero] = selectedHero.CurrentExp;
        }
        if (!heroPtsGained.ContainsKey(selectedHero))
        {
            heroPtsGained[selectedHero] = 0;
        }

        UpdateHeroExpUI();
        UpdatePtsProgressUI();

        // Add base stats
        AddStatToGrid("Squad", selectedHero.BaseStats.MarchCapacity.ToString());
        AddStatToGrid("Attack", selectedHero.BaseStats.Attack.ToString());
        AddStatToGrid("Defense", selectedHero.BaseStats.Defense.ToString());
        AddStatToGrid("Health", selectedHero.BaseStats.HP.ToString());

        // Add troop modifiers
        AddStatToGrid("Troop Atk", selectedHero.TroopModifiers.AttackBonus + "%");
        AddStatToGrid("Troop Def", selectedHero.TroopModifiers.DefenseBonus + "%");
        AddStatToGrid("Troop HP", selectedHero.TroopModifiers.HpBonus + "%");

        // Update skill images
        for (int i = 0; i < skillImages.Length && i < selectedHero.Skills.Length; i++)
        {
            skillImages[i].gameObject.SetActive(true);
            skillImages[i].sprite = selectedHero.Skills[i].skillIcon;

            // Calculate skill activation status
            bool isSkillActive;
            if (i == 0)
            {
                isSkillActive = true; // First skill always active
            }
            else if (i < 4)
            {
                isSkillActive = selectedHero.Rank > i; // Skills 2-4 require higher ranks
            }
            else // Fifth skill
            {
                isSkillActive = true;
                for (int j = 0; j < 4 && j < selectedHero.Skills.Length; j++)
                {
                    if (selectedHero.GetSkillLevel(j) < 5)
                    {
                        isSkillActive = false;
                        break;
                    }
                }
            }

            Material skillMaterial = skillImages[i].material;
            skillMaterial.SetFloat("_GrayscaleAmount", isSkillActive ? 0f : 1f);

            // Add level text if needed
            Transform levelTextObj = skillImages[i].transform.Find("LevelText");
            if (levelTextObj != null)
            {
                TextMeshProUGUI levelText = levelTextObj.GetComponent<TextMeshProUGUI>();
                int skillLevel = selectedHero.GetSkillLevel(i);

                if (isSkillActive)
                {
                    // For first skill, minimum level is 1
                    if (i == 0 && skillLevel < 1)
                    {
                        skillLevel = 1;
                    }
                    levelText.text = $"{skillLevel}";
                    levelTextObj.gameObject.SetActive(true);
                }
                else
                {
                    levelText.text = " ";
                    levelTextObj.gameObject.SetActive(true);
                }
            }
        }

        // Deactivate any remaining skill image slots
        for (int i = selectedHero.Skills.Length; i < skillImages.Length; i++)
        {
            skillImages[i].gameObject.SetActive(false);
        }

        // Update rank stars
        for (int i = 0; i < rankStars.Length; i++)
        {
            Material starMaterial = rankStars[i].material;
            if (i < selectedHero.Rank)
            {
                starMaterial.SetFloat("_GrayscaleAmount", 0f); // Set to colorful
                Color color = starMaterial.color;
                color.a = 1f; // Set alpha to 100%
                starMaterial.color = color;
            }
            else
            {
                starMaterial.SetFloat("_GrayscaleAmount", 1f); // Set to grayscale
                Color color = starMaterial.color;
                color.a = 0.3f; // Set alpha to 30%
                starMaterial.color = color;
            }
        }

        // Update used books text
        int requiredBattleBooks = selectedHero.CalculateRequiredBattleBooks(selectedHero.ExpLevel + 1);
        usedBooksText.text = heroExpGained[selectedHero] + "/" + requiredBattleBooks;

        // Update the circular progress bar
        float fillAmount = (float)heroExpGained[selectedHero] / requiredBattleBooks;
        expProgressBar.fillAmount = fillAmount;

        // Update PTs progress bar and text
        UpdatePtsProgressUI();

        // Show or hide upgrade buttons based on lock status
        upgradeRankButton.gameObject.SetActive(!selectedHero.IsLocked);
        upgradeExpButton.gameObject.SetActive(!selectedHero.IsLocked);
        upgradeSkillsButton.gameObject.SetActive(!selectedHero.IsLocked);

        // Show or hide recruit button and fragment text based on lock status
        recruitButton.gameObject.SetActive(selectedHero.IsLocked);
        fragmentText.gameObject.SetActive(selectedHero.IsLocked);
        fragmentImage.gameObject.SetActive(selectedHero.IsLocked);
        if (selectedHero.IsLocked)
        {
            int availableFragments = GetAvailableFragments(selectedHero.HeroName);
            fragmentText.text = $"{availableFragments}/{selectedHero.RequiredFragments}";
            UpdateFragmentImage(selectedHero.HeroName);
        }

        // Only setup videos when explicitly requested (initial selection)
        if (setupVideo)
        {
            SetupHeroVideos();
        }
    }

    public void RefreshUI()
    {
        if (selectedHero != null)
        {
            // When refreshing UI, we do want to reload progress from GameManager
            UpdateSelectedHeroUI(false, true);
        }
        PopulateHeroList();
    }

    private void SetupHeroVideos()
    {
        if (videoSequencePlayer == null || selectedHero == null) return;

        // Hide fallback image initially
        if (fallbackImageDisplay != null)
        {
            fallbackImageDisplay.gameObject.SetActive(false);
        }

        // Clear the RenderTexture to prevent showing the last frame
        if (videoSequencePlayer.videoPlayer != null &&
            videoSequencePlayer.videoPlayer.targetTexture != null)
        {
            RenderTexture.active = videoSequencePlayer.videoPlayer.targetTexture;
            GL.Clear(true, true, Color.clear);
            RenderTexture.active = null;
        }

        // Show RawImage for video
        heroDisplayRawImage.gameObject.SetActive(true);

        // Subscribe to video failure event
        videoSequencePlayer.OnVideoPlaybackFailed += HandleVideoPlaybackFailure;

        // Assign the videos to the video sequence player
        videoSequencePlayer.firstVideo = selectedHero.IntroVideo;
        videoSequencePlayer.secondVideo = selectedHero.LoopVideo;

        // Make sure the video player component is properly set up
        if (videoSequencePlayer.videoPlayer == null)
        {
            Debug.LogError("Video Player component not assigned in VideoSequencePlayer!");
            HandleVideoPlaybackFailure();
            return;
        }

        // Stop any current playback
        videoSequencePlayer.videoPlayer.Stop();

        // Reset and start the video sequence player
        videoSequencePlayer.enabled = false;
        videoSequencePlayer.enabled = true;
    }

    private void HandleVideoPlaybackFailure()
    {
        // Unsubscribe from the event
        videoSequencePlayer.OnVideoPlaybackFailed -= HandleVideoPlaybackFailure;

        // Hide video display
        heroDisplayRawImage.gameObject.SetActive(false);

        // Show fallback image if available
        if (fallbackImageDisplay != null && selectedHero.FallbackImage != null)
        {
            fallbackImageDisplay.gameObject.SetActive(true);
            fallbackImageDisplay.sprite = selectedHero.FallbackImage;
        }
        else
        {
            Debug.LogWarning($"No fallback image available for hero: {selectedHero.HeroName}");
        }
    }

    private void OnDisable()
    {
        // Clean up event subscription
        if (videoSequencePlayer != null)
        {
            videoSequencePlayer.OnVideoPlaybackFailed -= HandleVideoPlaybackFailure;
        }
    }

    private void AddStatToGrid(string label, string value)
    {
        GameObject statRow = Instantiate(statRowPrefab, statsGrid);
        TextMeshProUGUI[] texts = statRow.GetComponentsInChildren<TextMeshProUGUI>();
        texts[0].text = label;
        texts[1].text = value;
    }

    // private void InstantiateHeroPrefab()
    // {
    //     // Clear existing hero prefab
    //     foreach (Transform child in heroPrefabContainer)
    //     {
    //         Destroy(child.gameObject);
    //     }
    //
    //     if (selectedHero.HeroPrefab != null)
    //     {
    //         // Instantiate the hero prefab
    //         GameObject heroInstance = Instantiate(selectedHero.HeroPrefab, heroPrefabContainer);
    //         heroInstance.transform.localPosition = Vector3.zero;

    //         // Set the hero instance to be rendered by the hero display camera
    //         heroDisplayCamera.targetTexture = heroDisplayRawImage.texture as RenderTexture;
    //         heroDisplayCamera.gameObject.SetActive(true);
    //     }
    // }


    private void OnUpgradeRankButtonClicked()
    {
        // Update the thumbnail UI
        if (selectedThumbnailItem != null)
        {
            selectedThumbnailItem.UpdateUI();
        }

        // Open the rank upgrade UI
        OpenRankUpgradeUI();
    }

    private void OnUpgradeExpButtonClicked()
    {
        // Open the Hero Level Up UI
        OpenHeroLevelUpUI();
    }

    private void OnUpgradeSkillsButtonClicked()
    {
        // Open the skill upgrade UI
        skillUpgradeUI.OpenSkillUpgradeUI(selectedHero);
    }

    private void OpenRankUpgradeUI()
    {
        // Implement the logic to open the rank upgrade UI
        Debug.Log("Open Rank Upgrade UI");
        heroRankUI.SetActive(true);
        HideUIElements();

        StartCoroutine(SmoothTransform(objectOne, objectOneDefaultScale, new Vector3(1.2f, 1.2f, 1.2f),
            objectOneDefaultPosition, new Vector3(100f, 0f, 0f), 0.3f));
        StartCoroutine(SmoothTransform(objectTwo, objectTwoDefaultScale, new Vector3(1.5f, 1.5f, 1.5f),
            objectTwoDefaultPosition, new Vector3(-250f, -250f, 0f), 0.3f));
        cameraZoom.ZoomIn();
        PopulateBadges(); // Add this line to populate inventory items based on hero rarity
    }

    public void HideUIElements()
    {

        closeHeroButton.gameObject.SetActive(false);
        heroPanel.SetActive(false);
        heroPower.SetActive(false);
        heroSort.SetActive(false);
        scrollView.SetActive(false);

    }

    public void ShowUIElements()
    {
        closeHeroButton.gameObject.SetActive(true);
        heroPanel.SetActive(true);
        heroPower.SetActive(true);
        heroSort.SetActive(true);
        scrollView.SetActive(true);
    }


    private void OpenHeroLevelUpUI()
    {
        Debug.Log("Opening Hero Level Up UI");
        if (InventorySystem.Instance == null)
        {
            Debug.Log("InventorySystem instance is null");
            return;
        }
        heroLevelUpUI.SetActive(true);
        PopulateBookList();
    }

    private void OpenSkillUpgradeUI()
    {
        // Implement the logic to open the skill upgrade UI
        Debug.Log("Open Skill Upgrade UI");
        skillUpgradeUI.OpenSkillUpgradeUI(selectedHero);

    }

    private void CloseSkillUpgradeUI()
    {
        heroRankUI.SetActive(false);
        cameraZoom.ZoomOut();
    }

    private void PopulateBookList()
    {
        // Clear existing book list
        foreach (Transform child in bookListContainer)
        {
            Destroy(child.gameObject);
        }

        // Get books from the inventory
        Debug.Log("Starting to populate book list");
        var books = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Hero)
            .Where(item => item.itemSO.Type == "Book").ToList();
        Debug.Log($"Found {books.Count} books in inventory");

        // Populate the book list with available books
        foreach (InventoryItem book in books)
        {
            // Create a new GameObject for the book item
            GameObject bookItem = new GameObject("BookItem");
            bookItem.transform.SetParent(bookListContainer, false); // Set the parent and keep the local transform

            // Add a RectTransform component to the book item
            RectTransform rectTransform = bookItem.AddComponent<RectTransform>();

            // Add a VerticalLayoutGroup component to the book item
            VerticalLayoutGroup layoutGroup = bookItem.AddComponent<VerticalLayoutGroup>();
            layoutGroup.childAlignment = TextAnchor.MiddleCenter;
            layoutGroup.childForceExpandHeight = false;
            layoutGroup.childForceExpandWidth = false;
            layoutGroup.spacing = 20; // Adjust the spacing as needed

            // Add an Image component for the book image
            GameObject imageObject = new GameObject("BookImage");
            imageObject.transform.SetParent(bookItem.transform, false);
            Image bookImage = imageObject.AddComponent<Image>();
            bookImage.sprite = book.itemSO.Icon;

            // Add a Button component for the book button
            Button bookButton = bookItem.AddComponent<Button>();
            bookButton.onClick.AddListener(() => OnBookClicked(book));

            // Create a new GameObject for the book text
            GameObject textObject = new GameObject("BookText");
            textObject.transform.SetParent(bookItem.transform, false);

            // Add a RectTransform component to the text object
            RectTransform textRectTransform = textObject.AddComponent<RectTransform>();
            textRectTransform.anchorMin = new Vector2(0, 0);
            textRectTransform.anchorMax = new Vector2(1, 1);
            textRectTransform.offsetMin = new Vector2(0, 0);
            textRectTransform.offsetMax = new Vector2(0, 0);

            // Add a TextMeshProUGUI component for the book text
            TextMeshProUGUI bookText = textRectTransform.gameObject.AddComponent<TextMeshProUGUI>();
            bookText.text = $"<b>Owned: {book.quantity}</b>";
            bookText.fontSize = 24;
            bookText.alignment = TextAlignmentOptions.Center;

            // Set the text color using a hexadecimal color code
            Color textColor;
            if (ColorUtility.TryParseHtmlString("#c99251", out textColor)) // Example: Gold color
            {
                bookText.color = textColor;
            }
        }
    }

    private void OnBookClicked(InventoryItem book)
    {
        if (book.quantity > 0)
        {
            book.quantity--;
            heroExpGained[selectedHero] += book.itemSO.Value;
            ProcessExpGained(book.itemSO.Value);
            PopulateBookList();
        }
    }

    private void ProcessExpGained(int expToAdd)
    {
        if (!heroExpGained.ContainsKey(selectedHero))
        {
            heroExpGained[selectedHero] = 0;
        }

        int totalExp = expToAdd;
        while (totalExp > 0)
        {
            int requiredExp = selectedHero.CalculateRequiredBattleBooks(selectedHero.ExpLevel + 1) - selectedHero.CurrentExp;
            if (totalExp >= requiredExp)
            {
                selectedHero.UpgradeExp(requiredExp);
                totalExp -= requiredExp;
            }
            else
            {
                selectedHero.UpgradeExp(totalExp);
                totalExp = 0;
            }
        }
        heroExpGained[selectedHero] = selectedHero.CurrentExp;
        UpdateHeroExpUI();

        // Don't setup video or reload progress when updating after exp gain
        UpdateSelectedHeroUI(false, false);
        UpdateSelectedThumbnailUI();

        // Save the updated hero progress to GameManager
        GameManager.Instance.UpdateHeroProgress(selectedHero);
    }



    private void UpdateHeroExpUI()
    {
        int requiredBattleBooks = selectedHero.CalculateRequiredBattleBooks(selectedHero.ExpLevel + 1);
        usedBooksText.text = selectedHero.CurrentExp + "/" + requiredBattleBooks;
        float fillAmount = (float)selectedHero.CurrentExp / requiredBattleBooks;
        expProgressBar.fillAmount = fillAmount;
    }

    private void CloseHeroUI()
    {
        heroUI.SetActive(false);

    }

    private void CloseHeroLevelUpUI()
    {
        heroLevelUpUI.GetComponent<UIElementAnimator>().HandleDeactivation();
    }

    private void CloseHeroRankUI()
    {
        StartCoroutine(SmoothTransform(objectOne, objectOne.localScale, objectOneDefaultScale,
            objectOne.localPosition, objectOneDefaultPosition, 0.4f));
        StartCoroutine(SmoothTransform(objectTwo, objectTwo.localScale, objectTwoDefaultScale,
            objectTwo.localPosition, objectTwoDefaultPosition, 0.4f));
        heroRankUI.SetActive(false);
        ShowUIElements();
        cameraZoom.ZoomOut();
    }

    private void OnLevelUpButtonClicked()
    {
        int requiredExp = selectedHero.CalculateRequiredBattleBooks(selectedHero.ExpLevel + 1) - selectedHero.CurrentExp;
        List<InventoryItem> books = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Hero)
            .Where(item => item.itemSO.Tag == "Book" && item.quantity > 0)
            .OrderBy(item => item.itemSO.Value) // Use books with lower EXP first
            .ToList();

        int totalExp = 0;
        Dictionary<InventoryItem, int> booksToUse = new Dictionary<InventoryItem, int>();

        foreach (var book in books)
        {
            while (book.quantity > 0 && totalExp < requiredExp)
            {
                totalExp += book.itemSO.Value;
                book.quantity--;
                if (booksToUse.ContainsKey(book))
                {
                    booksToUse[book]++;
                }
                else
                {
                    booksToUse[book] = 1;
                }
            }

            if (totalExp >= requiredExp)
            {
                break;
            }
        }

        if (totalExp >= requiredExp)
        {
            foreach (var book in booksToUse)
            {
                heroExpGained[selectedHero] += book.Key.itemSO.Value * book.Value;
            }
            ProcessExpGained(totalExp);
            PopulateBookList(); // Update the book list to reflect the used books
        }
        else
        {
            Debug.Log("Not enough EXP books to level up.");
        }
    }

    private void OnRankUpButtonClicked()
    {
        int requiredPts = selectedHero.CalculateRequiredPtsForNextRank();
        ItemRarity badgeRarity = (ItemRarity)selectedHero.Rarity;

        List<InventoryItem> badges = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Hero)
            .Where(item =>
                item.itemSO.Type == "Badge" &&
                item.itemSO.Rarity == badgeRarity &&
                item.quantity > 0)
            .OrderBy(item => item.itemSO.Value)
            .ToList();

        int totalPts = 0;
        Dictionary<InventoryItem, int> badgesToUse = new Dictionary<InventoryItem, int>();

        foreach (var badge in badges)
        {
            while (badge.quantity > 0 && totalPts < requiredPts)
            {
                totalPts += badge.itemSO.Value;
                badge.quantity--;
                if (badgesToUse.ContainsKey(badge))
                {
                    badgesToUse[badge]++;
                }
                else
                {
                    badgesToUse[badge] = 1;
                }
            }

            if (totalPts >= requiredPts)
            {
                break;
            }
        }

        if (totalPts >= requiredPts)
        {
            // Use the badges and update the hero's PTs
            foreach (var badge in badgesToUse)
            {
                heroPtsGained[selectedHero] += badge.Key.itemSO.Value * badge.Value;
            }
            ProcessPtsGained(totalPts); // Pass the correct parameter
            PopulateBadges(); // Update the badge list to reflect the used badges
        }
        else
        {
            Debug.Log("Not enough PT badges to upgrade rank.");
        }
    }

    private void ProcessPtsGained(int ptsToAdd)
    {
        if (!heroPtsGained.ContainsKey(selectedHero))
        {
            heroPtsGained[selectedHero] = 0;
        }

        int totalPts = ptsToAdd;
        while (totalPts > 0)
        {
            int requiredPts = selectedHero.CalculateRequiredPtsForNextRank() - heroPtsGained[selectedHero];
            if (totalPts >= requiredPts)
            {
                selectedHero.UpgradeRank(requiredPts);
                totalPts -= requiredPts;
                heroPtsGained[selectedHero] = 0; // Reset the PTs gained after rank up
            }
            else
            {
                heroPtsGained[selectedHero] += totalPts;
                totalPts = 0;
            }
        }
        UpdatePtsProgressUI();

        // Don't setup video or reload progress when updating after pts gain
        UpdateSelectedHeroUI(false, false);
        UpdateSelectedThumbnailUI(); // Add this line to update the thumbnail UI

        // Save progress to GameManager
        GameManager.Instance.UpdateHeroProgress(selectedHero);
    }




    private void OnRecruitButtonClicked()
    {
        int availableFragments = GetAvailableFragments(selectedHero.HeroName);
        if (availableFragments >= selectedHero.RequiredFragments)
        {
            selectedHero.IsLocked = false;
            PopulateHeroList();
            // Don't setup video or reload progress when updating after recruitment
            UpdateSelectedHeroUI(false, false);
        }
        else
        {
            Debug.Log("Not enough fragments to recruit.");
        }
    }

    private int GetAvailableFragments(string heroName)
    {
        // Find the fragment item in the inventory that matches the hero's name
        InventoryItem fragmentItem = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Hero)
            .FirstOrDefault(item => item.itemSO.Tag == heroName);

        return fragmentItem != null ? fragmentItem.quantity : 0;
    }

    private void RemoveFragmentsFromInventory(string heroName, int amount)
    {
        // Find the fragment item in the inventory that matches the hero's name
        InventoryItem fragmentItem = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Hero)
            .FirstOrDefault(item => item.itemSO.Tag == heroName);

        if (fragmentItem != null)
        {
            fragmentItem.quantity -= amount;
        }
    }

    private void UpdateFragmentImage(string heroName)
    {
        // Find the fragment item in the inventory that matches the hero's name
        InventoryItem fragmentItem = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Hero)
            .FirstOrDefault(item => item.itemSO.Tag == heroName);

        if (fragmentItem != null)
        {
            fragmentImage.sprite = fragmentItem.itemSO.Icon;
        }
        else
        {
            fragmentImage.sprite = null; // Clear the image if no fragment is found
        }
    }

    private void PopulateBadges()
    {
        foreach (Transform child in badgeListContainer)
        {
            Destroy(child.gameObject);
        }
        ItemRarity badgeRarity = (ItemRarity)selectedHero.Rarity;
        // Get badges matching hero's rarity
        List<InventoryItem> badges = InventorySystem.Instance.GetItemsByCategory(ItemCategory.Hero)
            .Where(item =>
                item.itemSO.Type == "Badge" &&
                item.itemSO.Rarity == badgeRarity)
            .ToList();

        foreach (InventoryItem badge in badges)
        {
            GameObject badgeItem = new GameObject("BadgeItem");
            badgeItem.transform.SetParent(badgeListContainer, false);

            Image badgeImage = badgeItem.AddComponent<Image>();
            badgeImage.sprite = badge.itemSO.Icon;

            Button badgeButton = badgeItem.AddComponent<Button>();
            badgeButton.onClick.AddListener(() => OnBadgeClicked(badge));

            GameObject textObject = new GameObject("BadgeText");
            textObject.transform.SetParent(badgeItem.transform, false);

            RectTransform textRectTransform = textObject.AddComponent<RectTransform>();
            textRectTransform.anchorMin = new Vector2(0, 0);
            textRectTransform.anchorMax = new Vector2(1, 1);
            textRectTransform.offsetMin = new Vector2(0, 0);
            textRectTransform.offsetMax = new Vector2(0, -220);

            TextMeshProUGUI badgeText = textRectTransform.gameObject.AddComponent<TextMeshProUGUI>();
            badgeText.text = $"<b>{badge.itemSO.Value}\nOwned: {badge.quantity}</b>";
            badgeText.fontSize = 24;
            badgeText.alignment = TextAlignmentOptions.Center;

            Color textColor;
            if (ColorUtility.TryParseHtmlString("#c99251", out textColor))
            {
                badgeText.color = textColor;
            }
        }
    }

    private void OnBadgeClicked(InventoryItem badge)
    {
        if (badge.quantity > 0)
        {
            badge.quantity--;
            ProcessPtsGained(badge.itemSO.Value); // Pass the correct parameter
            PopulateBadges();
        }
    }

    private void UpdatePtsProgressUI()
    {
        int requiredPts = selectedHero.CalculateRequiredPtsForNextRank();
        int currentPts = heroPtsGained[selectedHero];
        ptsProgressText.text = $"{currentPts}/{requiredPts} PTs";
        float fillAmount = (float)currentPts / requiredPts;
        ptsProgressBar.fillAmount = fillAmount;
    }

    private void UpdateSelectedThumbnailUI()
    {
        if (selectedThumbnailItem != null)
        {
            selectedThumbnailItem.UpdateUI();
        }
    }

    private IEnumerator SmoothTransform(Transform target, Vector3 startScale, Vector3 endScale,
    Vector3 startPos, Vector3 endPos, float duration)
    {
        float elapsed = 0;
        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float progress = elapsed / duration;

            target.localScale = Vector3.Lerp(startScale, endScale, progress);
            target.localPosition = Vector3.Lerp(startPos, endPos, progress);

            yield return null;
        }

        target.localScale = endScale;
        target.localPosition = endPos;
    }



}
